{"name": "nuxt-blog", "private": true, "type": "module", "scripts": {"start": "node start.js", "build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "clean": "nuxt cleanup", "analyze": "nuxt analyze", "typecheck": "nuxt typecheck", "lint": "eslint .", "lint:fix": "eslint . --fix", "fix-deps": "node scripts/fix-dependencies.js", "upgrade": "node scripts/upgrade-deps.js", "upgrade:simple": "pnpm up --latest", "clean:install": "node clean-install.js", "quick-start": "node quick-start.js"}, "dependencies": {"@nuxt/ui": "^3.3.0", "nuxt": "^4.0.2", "vue": "^3.5.18"}, "devDependencies": {"@antfu/eslint-config": "^5.1.0", "@nuxt/eslint": "^1.7.1", "@nuxtjs/tailwindcss": "^6.14.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.1.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "tailwindcss": "^4.1.11", "typescript": "^5.9.2", "vue-tsc": "^3.0.5"}, "pnpm": {"overrides": {"@nuxt/kit": "4.0.2"}}}