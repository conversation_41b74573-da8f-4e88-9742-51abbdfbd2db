<template>
  <button
    @click="toggleTheme"
    class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors"
    :title="isDark ? '切换到浅色模式' : '切换到深色模式'"
  >
    <span class="text-lg">
      {{ isDark ? '🌙' : '☀️' }}
    </span>
  </button>
</template>

<script setup lang="ts">
const isDark = ref(false)

onMounted(() => {
  // 检查初始主题
  const savedTheme = localStorage.getItem('theme')
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

  const shouldBeDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark)

  isDark.value = shouldBeDark
  applyTheme(shouldBeDark)
})

function applyTheme(dark: boolean) {
  const html = document.documentElement
  if (dark) {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

function toggleTheme() {
  isDark.value = !isDark.value
  applyTheme(isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}
</script>
