<template>
  <ClientOnly>
    <UButton
      :icon="isDark ? 'i-heroicons-moon-20-solid' : 'i-heroicons-sun-20-solid'"
      color="gray"
      variant="ghost"
      aria-label="切换主题"
      @click="toggleTheme"
    />
    <template #fallback>
      <UButton
        icon="i-heroicons-sun-20-solid"
        color="gray"
        variant="ghost"
        aria-label="切换主题"
      />
    </template>
  </ClientOnly>
</template>

<script setup lang="ts">
const colorMode = useColorMode()

const isDark = computed(() => colorMode.value === 'dark')

function toggleTheme() {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
  console.log('切换主题:', {
    preference: colorMode.preference,
    value: colorMode.value,
    forced: colorMode.forced
  })
}

// 监听颜色模式变化
watch(() => colorMode.value, (newValue) => {
  console.log('主题已切换到:', newValue)
})
</script>
