
@import "tailwindcss";

/* Nuxt 风格的绿色主题 */
:root {
  --nuxt-green: #00dc82;
  --nuxt-green-light: #34d399;
  --nuxt-green-dark: #059669;
}

/* 确保暗色模式正确应用 */
html.dark {
  color-scheme: dark;
}

html.light {
  color-scheme: light;
}

/* Nuxt 风格的渐变背景 */
.nuxt-gradient {
  background: linear-gradient(135deg, #00dc82 0%, #34d399 100%);
}

.nuxt-gradient-subtle {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

/* 暗色模式下的渐变 */
.dark .nuxt-gradient-subtle {
  background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
}

/* 测试暗色模式是否工作 */
.dark body,
.dark #__nuxt {
  background-color: #111827 !important;
  color: #f3f4f6 !important;
}

/* 强制应用到根元素 */
html.dark {
  background-color: #111827 !important;
  color: #f3f4f6 !important;
}
