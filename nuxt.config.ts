import tailwindcss from '@tailwindcss/vite'

export default defineNuxtConfig({
  devtools: { enabled: true },
  modules: [
    '@nuxt/ui',
    '@nuxt/eslint'
  ],
  css: ['@/assets/css/main.css'],
  vite: {
    plugins: [
      // 使用 Tailwind CSS 4.x 的 Vite 插件
      tailwindcss()
    ]
  },
  postcss: {
    plugins: {
      // 使用 Tailwind CSS 4.x 的 PostCSS 插件
      '@tailwindcss/postcss': {},
      autoprefixer: {}
    }
  },
  typescript: {
    typeCheck: false
  },
  compatibilityDate: '2024-11-20'
})
