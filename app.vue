<template>
  <div
    class="min-h-screen transition-colors duration-300"
    :class="[
      'bg-white text-gray-900',
      'dark:bg-gray-900 dark:text-gray-100'
    ]"
    :style="{
      backgroundColor: isDarkMode ? '#111827' : '#ffffff',
      color: isDarkMode ? '#f3f4f6' : '#111827'
    }"
  >
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
// 检测暗色模式
const isDarkMode = ref(false)

onMounted(() => {
  // 检查初始状态
  const checkDarkMode = () => {
    isDarkMode.value = document.documentElement.classList.contains('dark')
  }

  checkDarkMode()

  // 监听 DOM 变化
  const observer = new MutationObserver(() => {
    checkDarkMode()
  })

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 清理
  onUnmounted(() => {
    observer.disconnect()
  })
})

// 全局 SEO 配置
useSeoMeta({
  titleTemplate: '%s - Nuxt Blog',
  description: '基于 Nuxt 4.0 的现代博客，使用最新的前端技术栈构建',
  ogTitle: 'Nuxt Blog',
  ogDescription: '基于 Nuxt 4.0 的现代博客',
  ogImage: '/og-image.jpg',
  twitterCard: 'summary_large_image',
})

// 全局样式和配置
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  link: [
    {
      rel: 'preconnect',
      href: 'https://fonts.googleapis.com'
    },
    {
      rel: 'preconnect',
      href: 'https://fonts.gstatic.com',
      crossorigin: ''
    }
  ]
})
</script>
