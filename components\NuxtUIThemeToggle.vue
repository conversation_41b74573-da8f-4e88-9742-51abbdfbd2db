<template>
  <button
    @click="toggleTheme"
    style="padding: 8px; border-radius: 8px; background: #f3f4f6; border: 1px solid #d1d5db; cursor: pointer;"
    :title="isDark ? '切换到浅色模式' : '切换到深色模式'"
  >
    <span style="font-size: 18px;">
      {{ isDark ? '🌙' : '☀️' }}
    </span>
  </button>
</template>

<script setup lang="ts">
console.log('NuxtUIThemeToggle 组件加载')

// 直接操作 DOM 和 localStorage 的简单实现
const isDark = ref(false)

// 检查初始主题
onMounted(() => {
  console.log('onMounted 执行')
  try {
    // 检查 localStorage 中的主题设置
    const savedTheme = localStorage.getItem('theme')
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

    const shouldBeDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark)

    isDark.value = shouldBeDark
    applyTheme(shouldBeDark)

    console.log('初始主题状态:', { isDark: isDark.value, savedTheme, systemPrefersDark })
  } catch (error) {
    console.error('onMounted 错误:', error)
  }
})

function applyTheme(dark: boolean) {
  console.log('applyTheme 调用:', dark)
  try {
    const html = document.documentElement
    if (dark) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
    console.log('HTML 类名:', html.className)
  } catch (error) {
    console.error('applyTheme 错误:', error)
  }
}

function toggleTheme() {
  console.log('=== 点击切换按钮 ===')
  console.log('点击前状态:', isDark.value)

  try {
    isDark.value = !isDark.value
    applyTheme(isDark.value)

    // 保存到 localStorage
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')

    console.log('切换后状态:', {
      isDark: isDark.value,
      htmlClass: document.documentElement.className,
      localStorage: localStorage.getItem('theme')
    })
  } catch (error) {
    console.error('toggleTheme 错误:', error)
  }
}
</script>
