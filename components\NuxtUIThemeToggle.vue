<template>
  <button
    class="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors duration-200 border border-gray-200 dark:border-gray-600"
    @click="toggleTheme"
    :title="isDark ? '切换到浅色模式' : '切换到深色模式'"
  >
    <Icon
      :name="isDark ? 'heroicons:moon-20-solid' : 'heroicons:sun-20-solid'"
      class="w-5 h-5 text-gray-600 dark:text-gray-300"
    />
  </button>
</template>

<script setup lang="ts">
// 直接操作 DOM 和 localStorage 的简单实现
const isDark = ref(false)

// 检查初始主题
onMounted(() => {
  // 检查 localStorage 中的主题设置
  const savedTheme = localStorage.getItem('theme')
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches

  const shouldBeDark = savedTheme === 'dark' || (!savedTheme && systemPrefersDark)

  isDark.value = shouldBeDark
  applyTheme(shouldBeDark)

  console.log('初始主题状态:', { isDark: isDark.value, savedTheme, systemPrefersDark })
})

function applyTheme(dark: boolean) {
  const html = document.documentElement
  if (dark) {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

function toggleTheme() {
  console.log('点击切换按钮，当前状态:', isDark.value)

  isDark.value = !isDark.value
  applyTheme(isDark.value)

  // 保存到 localStorage
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')

  console.log('切换后状态:', {
    isDark: isDark.value,
    htmlClass: document.documentElement.className,
    localStorage: localStorage.getItem('theme')
  })
}
</script>
