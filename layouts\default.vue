<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 侧边栏 -->
    <aside class="fixed left-0 top-0 h-full w-64 bg-white dark:bg-gray-800 shadow-lg z-40 transform transition-transform duration-300" :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'">
      <div class="p-6">
        <!-- Logo -->
        <div class="flex items-center space-x-3 mb-8">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <span class="text-white font-bold text-lg">M</span>
          </div>
          <span class="text-xl font-bold text-gray-900 dark:text-white">Meepo</span>
        </div>

        <!-- 导航菜单 -->
        <nav class="space-y-2">
          <NuxtLink to="/" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" :class="$route.path === '/' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : ''">
            <Icon name="heroicons:home" class="w-5 h-5" />
            <span>Home</span>
          </NuxtLink>

          <NuxtLink to="/archive" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <Icon name="heroicons:archive-box" class="w-5 h-5" />
            <span>Archive</span>
          </NuxtLink>

          <NuxtLink to="/about" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <Icon name="heroicons:user" class="w-5 h-5" />
            <span>About</span>
          </NuxtLink>
        </nav>

        <!-- 更多功能 -->
        <div class="mt-8">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">More Features</h3>
          <nav class="space-y-2">
            <a href="#" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <Icon name="heroicons:star" class="w-5 h-5" />
              <span>Recommendations</span>
            </a>
          </nav>
        </div>

        <!-- 标签 -->
        <div class="mt-8">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Tags</h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">技术总结</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">独立开发</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">SEO</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">Deploy</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-cyan-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">支付</span>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="lg:ml-64">
      <!-- 顶部栏 -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- 移动端菜单按钮 -->
            <button @click="toggleSidebar" class="lg:hidden p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
              <Icon name="heroicons:bars-3" class="w-6 h-6" />
            </button>

            <!-- 搜索栏 -->
            <div class="flex-1 max-w-2xl mx-auto">
              <div class="relative">
                <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search"
                  class="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:bg-white dark:focus:bg-gray-600"
                />
                <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 rounded">⌘K</kbd>
              </div>
            </div>

            <!-- 右侧按钮 -->
            <div class="flex items-center space-x-3">
              <NuxtUIThemeToggle />
              <button class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Icon name="heroicons:user-circle" class="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <slot />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div v-if="sidebarOpen" @click="closeSidebar" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"></div>
  </div>
</template>

<script setup lang="ts">
const sidebarOpen = ref(false)

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

// 监听路由变化，关闭侧边栏
const route = useRoute()
watch(() => route.path, () => {
  sidebarOpen.value = false
})
</script>
