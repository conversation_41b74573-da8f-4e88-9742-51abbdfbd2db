<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 侧边栏 -->
    <aside
      class="fixed left-0 top-0 h-full bg-white dark:bg-gray-800 shadow-lg z-40 transition-all duration-300"
      :class="[
        sidebarCollapsed ? 'w-16' : 'w-64',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      ]"
    >
      <div class="p-6">
        <!-- Logo -->
        <div class="flex items-center mb-8" :class="sidebarCollapsed ? 'justify-center' : 'space-x-3'">
          <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <span class="text-white font-bold text-lg">N</span>
          </div>
          <span v-if="!sidebarCollapsed" class="text-xl font-bold text-gray-900 dark:text-white transition-opacity duration-300">Nuxt Blog</span>
        </div>

        <!-- 收缩/展开按钮 -->
        <div class="mb-6" :class="sidebarCollapsed ? 'text-center' : ''">
          <button
            @click="toggleSidebarCollapse"
            class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            :title="sidebarCollapsed ? '展开侧边栏' : '收缩侧边栏'"
          >
            <Icon
              :name="sidebarCollapsed ? 'heroicons:chevron-right' : 'heroicons:chevron-left'"
              class="w-5 h-5 text-gray-600 dark:text-gray-300"
            />
          </button>
        </div>

        <!-- 导航菜单 -->
        <nav class="space-y-2">
          <NuxtLink
            to="/"
            class="flex items-center rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            :class="[
              $route.path === '/' ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400' : '',
              sidebarCollapsed ? 'justify-center p-3' : 'space-x-3 px-4 py-3'
            ]"
            :title="sidebarCollapsed ? 'Home' : ''"
          >
            <Icon name="heroicons:home" class="w-5 h-5 flex-shrink-0" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-300">Home</span>
          </NuxtLink>

          <NuxtLink
            to="/archive"
            class="flex items-center rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            :class="[
              $route.path === '/archive' ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400' : '',
              sidebarCollapsed ? 'justify-center p-3' : 'space-x-3 px-4 py-3'
            ]"
            :title="sidebarCollapsed ? 'Archive' : ''"
          >
            <Icon name="heroicons:archive-box" class="w-5 h-5 flex-shrink-0" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-300">Archive</span>
          </NuxtLink>

          <NuxtLink
            to="/about"
            class="flex items-center rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            :class="[
              $route.path === '/about' ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400' : '',
              sidebarCollapsed ? 'justify-center p-3' : 'space-x-3 px-4 py-3'
            ]"
            :title="sidebarCollapsed ? 'About' : ''"
          >
            <Icon name="heroicons:user" class="w-5 h-5 flex-shrink-0" />
            <span v-if="!sidebarCollapsed" class="transition-opacity duration-300">About</span>
          </NuxtLink>
        </nav>

        <!-- 更多功能 -->
        <div v-if="!sidebarCollapsed" class="mt-8">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">More Features</h3>
          <nav class="space-y-2">
            <a href="#" class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <Icon name="heroicons:star" class="w-5 h-5" />
              <span>Recommendations</span>
            </a>
          </nav>
        </div>

        <!-- 收缩状态下的更多功能 -->
        <div v-if="sidebarCollapsed" class="mt-8">
          <a href="#" class="flex justify-center p-3 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" title="Recommendations">
            <Icon name="heroicons:star" class="w-5 h-5" />
          </a>
        </div>

        <!-- 标签 -->
        <div v-if="!sidebarCollapsed" class="mt-8">
          <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Tags</h3>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">技术总结</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">独立开发</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-teal-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">SEO</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-lime-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">Deploy</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-cyan-500 rounded-full"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">支付</span>
            </div>
          </div>
        </div>

        <!-- 收缩状态下的标签 -->
        <div v-if="sidebarCollapsed" class="mt-8 space-y-2">
          <div class="flex justify-center" title="技术总结">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <div class="flex justify-center" title="独立开发">
            <div class="w-3 h-3 bg-emerald-500 rounded-full"></div>
          </div>
          <div class="flex justify-center" title="SEO">
            <div class="w-3 h-3 bg-teal-500 rounded-full"></div>
          </div>
          <div class="flex justify-center" title="Deploy">
            <div class="w-3 h-3 bg-lime-500 rounded-full"></div>
          </div>
          <div class="flex justify-center" title="支付">
            <div class="w-3 h-3 bg-cyan-500 rounded-full"></div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="transition-all duration-300" :class="sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'">
      <!-- 顶部栏 -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- 移动端菜单按钮 -->
            <button @click="toggleSidebar" class="lg:hidden p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
              <Icon name="heroicons:bars-3" class="w-6 h-6" />
            </button>

            <!-- 搜索栏 -->
            <div class="flex-1 max-w-2xl mx-auto">
              <div class="relative">
                <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search"
                  class="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-gray-600"
                />
                <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 rounded">⌘K</kbd>
              </div>
            </div>

            <!-- 右侧按钮 -->
            <div class="flex items-center space-x-3">
              <NuxtUIThemeToggle />
              <button class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Icon name="heroicons:user-circle" class="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <slot />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div v-if="sidebarOpen" @click="closeSidebar" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"></div>
  </div>
</template>

<script setup lang="ts">
const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存状态到 localStorage
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
}

// 从 localStorage 恢复侧边栏状态
onMounted(() => {
  const saved = localStorage.getItem('sidebarCollapsed')
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true'
  }
})

// 监听路由变化，关闭移动端侧边栏
const route = useRoute()
watch(() => route.path, () => {
  sidebarOpen.value = false
})
</script>
