<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 侧边栏 -->
    <aside
      class="fixed left-0 top-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-40 transition-all duration-300 ease-in-out"
      :class="[
        sidebarCollapsed ? 'w-20' : 'w-72',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      ]"
    >
      <div class="flex flex-col h-full">
        <!-- 顶部区域 -->
        <div class="p-6 border-b border-gray-100 dark:border-gray-700">
          <!-- Logo -->
          <div class="flex items-center" :class="sidebarCollapsed ? 'justify-center' : 'justify-between'">
            <div class="flex items-center" :class="sidebarCollapsed ? '' : 'space-x-3'">
              <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                <span class="text-white font-bold text-lg">N</span>
              </div>
              <div v-if="!sidebarCollapsed" class="transition-all duration-300">
                <h1 class="text-xl font-bold text-gray-900 dark:text-white">Nuxt Blog</h1>
                <p class="text-xs text-gray-500 dark:text-gray-400">现代化博客</p>
              </div>
            </div>

            <!-- 收缩按钮 -->
            <button
              v-if="!sidebarCollapsed"
              @click="toggleSidebarCollapse"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors group"
              title="收缩侧边栏"
            >
              <Icon
                name="heroicons:chevron-left"
                class="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300"
              />
            </button>
          </div>
        </div>

        <!-- 导航区域 -->
        <div class="flex-1 p-4 overflow-y-auto">
          <!-- 展开按钮（收缩状态下） -->
          <div v-if="sidebarCollapsed" class="mb-6 flex justify-center">
            <button
              @click="toggleSidebarCollapse"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors group"
              title="展开侧边栏"
            >
              <Icon
                name="heroicons:chevron-right"
                class="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300"
              />
            </button>
          </div>

          <!-- 主导航 -->
          <nav class="space-y-1">
            <div v-if="!sidebarCollapsed" class="px-3 mb-4">
              <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">导航</h3>
            </div>

            <NuxtLink
              to="/"
              class="group flex items-center rounded-xl font-medium transition-all duration-200"
              :class="[
                $route.path === '/'
                  ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/10 hover:text-green-600 dark:hover:text-green-400',
                sidebarCollapsed ? 'justify-center p-3 mx-2' : 'px-4 py-3'
              ]"
            >
              <Icon
                name="heroicons:home"
                class="flex-shrink-0 transition-transform duration-200"
                :class="[
                  sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5',
                  $route.path === '/' ? '' : 'group-hover:scale-110'
                ]"
              />
              <span v-if="!sidebarCollapsed" class="ml-3 transition-all duration-300">首页</span>

              <!-- 收缩状态下的提示 -->
              <div v-if="sidebarCollapsed" class="absolute left-full ml-6 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                首页
              </div>
            </NuxtLink>

            <NuxtLink
              to="/archive"
              class="group flex items-center rounded-xl font-medium transition-all duration-200"
              :class="[
                $route.path === '/archive'
                  ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/10 hover:text-green-600 dark:hover:text-green-400',
                sidebarCollapsed ? 'justify-center p-3 mx-2' : 'px-4 py-3'
              ]"
            >
              <Icon
                name="heroicons:archive-box"
                class="flex-shrink-0 transition-transform duration-200"
                :class="[
                  sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5',
                  $route.path === '/archive' ? '' : 'group-hover:scale-110'
                ]"
              />
              <span v-if="!sidebarCollapsed" class="ml-3 transition-all duration-300">归档</span>

              <div v-if="sidebarCollapsed" class="absolute left-full ml-6 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                归档
              </div>
            </NuxtLink>

            <NuxtLink
              to="/about"
              class="group flex items-center rounded-xl font-medium transition-all duration-200"
              :class="[
                $route.path === '/about'
                  ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/10 hover:text-green-600 dark:hover:text-green-400',
                sidebarCollapsed ? 'justify-center p-3 mx-2' : 'px-4 py-3'
              ]"
            >
              <Icon
                name="heroicons:user"
                class="flex-shrink-0 transition-transform duration-200"
                :class="[
                  sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5',
                  $route.path === '/about' ? '' : 'group-hover:scale-110'
                ]"
              />
              <span v-if="!sidebarCollapsed" class="ml-3 transition-all duration-300">关于</span>

              <div v-if="sidebarCollapsed" class="absolute left-full ml-6 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                关于
              </div>
            </NuxtLink>
          </nav>

          <!-- 快捷功能 -->
          <div class="mt-8">
            <div v-if="!sidebarCollapsed" class="px-3 mb-4">
              <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">快捷功能</h3>
            </div>

            <a
              href="#"
              class="group flex items-center rounded-xl font-medium transition-all duration-200 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/10 hover:text-green-600 dark:hover:text-green-400"
              :class="sidebarCollapsed ? 'justify-center p-3 mx-2' : 'px-4 py-3'"
            >
              <Icon
                name="heroicons:star"
                class="flex-shrink-0 transition-transform duration-200 group-hover:scale-110"
                :class="sidebarCollapsed ? 'w-6 h-6' : 'w-5 h-5'"
              />
              <span v-if="!sidebarCollapsed" class="ml-3 transition-all duration-300">推荐</span>

              <div v-if="sidebarCollapsed" class="absolute left-full ml-6 px-2 py-1 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 pointer-events-none whitespace-nowrap z-50">
                推荐
              </div>
            </a>
          </div>
        </div>

        <!-- 底部区域 -->
        <div class="p-4 border-t border-gray-100 dark:border-gray-700">
          <div class="flex items-center" :class="sidebarCollapsed ? 'justify-center' : 'space-x-3'">
            <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-medium text-sm">U</span>
            </div>
            <div v-if="!sidebarCollapsed" class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">用户</p>
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate"><EMAIL></p>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="transition-all duration-300 ease-in-out" :class="sidebarCollapsed ? 'lg:ml-20' : 'lg:ml-72'">
      <!-- 顶部栏 -->
      <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- 移动端菜单按钮 -->
            <button @click="toggleSidebar" class="lg:hidden p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
              <Icon name="heroicons:bars-3" class="w-6 h-6" />
            </button>

            <!-- 搜索栏 -->
            <div class="flex-1 max-w-2xl mx-auto">
              <div class="relative">
                <Icon name="heroicons:magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search"
                  class="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border-0 rounded-lg text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-gray-600"
                />
                <kbd class="absolute right-3 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs text-gray-500 bg-gray-200 dark:bg-gray-600 rounded">⌘K</kbd>
              </div>
            </div>

            <!-- 右侧按钮 -->
            <div class="flex items-center space-x-3">
              <NuxtUIThemeToggle />
              <button class="p-2 rounded-lg text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700">
                <Icon name="heroicons:user-circle" class="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <slot />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div v-if="sidebarOpen" @click="closeSidebar" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"></div>
  </div>
</template>

<script setup lang="ts">
const sidebarOpen = ref(false)
const sidebarCollapsed = ref(false)

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleSidebarCollapse = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存状态到 localStorage
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
}

// 从 localStorage 恢复侧边栏状态
onMounted(() => {
  const saved = localStorage.getItem('sidebarCollapsed')
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true'
  }
})

// 监听路由变化，关闭移动端侧边栏
const route = useRoute()
watch(() => route.path, () => {
  sidebarOpen.value = false
})
</script>
