<template>
  <div>
    <!-- 顶部导航标签 -->
    <div class="mb-8">
      <div class="flex items-center space-x-8 border-b border-gray-200 dark:border-gray-700">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          class="px-4 py-3 text-sm font-medium border-b-2 transition-colors"
          :class="activeTab === tab.id
            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
            : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'"
        >
          {{ tab.name }}
        </button>
      </div>
    </div>

    <!-- 主要特色文章 -->
    <div class="mb-8">
      <div class="relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden group hover:shadow-xl transition-all duration-300">
        <div class="flex flex-col lg:flex-row">
          <!-- 左侧内容 -->
          <div class="flex-1 p-8">
            <div class="flex items-center space-x-3 mb-6">
              <img src="https://via.placeholder.com/48x48" alt="Avatar" class="w-12 h-12 rounded-full">
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white">Meepo</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400">Mar 17, 2025</p>
              </div>
            </div>

            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              要找一份远程前端（全栈）开发的工作
            </h2>

            <p class="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
              为什么会选择远程工作？一直是我想要的工作方式，虽然本人从事前端开发的时间，都是异地合作的，大部分...
            </p>

            <button class="inline-flex items-center px-6 py-3 bg-gray-900 dark:bg-white text-white dark:text-gray-900 rounded-lg font-medium hover:bg-gray-800 dark:hover:bg-gray-100 transition-colors">
              Read More
              <Icon name="heroicons:arrow-right" class="ml-2 w-4 h-4" />
            </button>
          </div>

          <!-- 右侧图片 -->
          <div class="lg:w-96 h-64 lg:h-auto">
            <img
              src="https://via.placeholder.com/400x300"
              alt="Featured post"
              class="w-full h-full object-cover"
            >
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 px-3 py-1 rounded-full text-sm font-medium text-gray-700 dark:text-gray-300">
              5 min read
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 小卡片网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- WildCard 卡片 -->
      <div class="bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer">
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <img src="https://via.placeholder.com/32x32" alt="AWS" class="w-8 h-8 rounded">
            <img src="https://via.placeholder.com/32x32" alt="Apple" class="w-8 h-8 rounded">
            <img src="https://via.placeholder.com/32x32" alt="WildCard" class="w-8 h-8 rounded">
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400">4 min read</span>
        </div>

        <h3 class="font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          WildCard 虚拟信用卡 - 海外 AI 工具订阅
        </h3>

        <div class="flex items-center space-x-2 mt-4">
          <img src="https://via.placeholder.com/24x24" alt="Author" class="w-6 h-6 rounded-full">
          <span class="text-sm text-gray-600 dark:text-gray-400">Meepo 有想法</span>
        </div>
      </div>

      <!-- 跨境支付卡片 -->
      <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer border border-gray-200 dark:border-gray-700">
        <div class="flex items-start justify-between mb-4">
          <div class="w-full h-32 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
            <span class="text-white font-bold text-lg">💳</span>
          </div>
          <span class="text-xs text-gray-500 dark:text-gray-400 ml-4">3 min read</span>
        </div>

        <h3 class="font-bold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          跨境支付，内地 <-> 香港港币
        </h3>

        <div class="flex items-center space-x-2 mt-4">
          <img src="https://via.placeholder.com/24x24" alt="Author" class="w-6 h-6 rounded-full">
          <span class="text-sm text-gray-600 dark:text-gray-400">无先验知识</span>
        </div>
      </div>

      <!-- Stripe 卡片 -->
      <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 group cursor-pointer text-white">
        <div class="flex items-start justify-between mb-4">
          <div class="text-4xl font-bold">stripe</div>
          <div class="bg-white/20 px-2 py-1 rounded text-sm">67</div>
          <span class="text-xs text-white/70">9 min read</span>
        </div>

        <h3 class="font-bold mb-2 group-hover:text-yellow-200 transition-colors">
          Stripe 申请全流程 - 出海收款必备
        </h3>

        <div class="flex items-center space-x-2 mt-4">
          <img src="https://via.placeholder.com/24x24" alt="Author" class="w-6 h-6 rounded-full">
          <span class="text-sm text-white/80">出海收款专家</span>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
// 页面元数据
useHead({
  title: 'Home - Meepo Blog'
})

// 标签页数据
const tabs = [
  { id: 'all', name: 'All Posts' },
  { id: 'tech', name: '技术总结' },
  { id: 'dev', name: '独立开发' },
  { id: 'seo', name: 'SEO' },
  { id: 'deploy', name: 'Deploy' },
  { id: 'payment', name: '支付' },
  { id: 'ai', name: 'AI' },
  { id: 'nodejs', name: 'Nodejs' },
  { id: 'cloudflare', name: 'Cloudflare' }
]

const activeTab = ref('all')
</script>

<style scoped>
/* 动画效果 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 拉伸链接效果 */
.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}
</style>
