<template>
  <div class="flex items-center space-x-4">
    <button
      class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      @click="testClick"
    >
      测试点击
    </button>

    <button
      class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      @click="toggleTheme"
    >
      {{ isDark ? '切换到亮色' : '切换到暗色' }}
    </button>

    <span class="text-sm">
      当前: {{ $colorMode.value }}
    </span>
  </div>
</template>

<script setup lang="ts">
const colorMode = useColorMode()
const isDark = computed(() => colorMode.value === 'dark')

function testClick() {
  alert('点击测试成功！')
  console.log('测试点击成功')
}

function toggleTheme() {
  console.log('切换主题被调用')
  console.log('当前主题:', colorMode.value)

  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'

  console.log('新的主题偏好:', colorMode.preference)
}
</script>
